# MQTT Implementation

This module implements MQTT functionality for device communication based on the provided specifications.

## Configuration

Add the following variables to your `.env` file:

```env
# MQTT Configuration
MQTT_HOST=*************
MQTT_PORT=1883
MQTT_USERNAME=1234
MQTT_PASSWORD=1234
MQTT_CLIENT_ID=1234
```

## Supported Commands

### Electronic Commands
Topic: `devices/{clientid}/electronic/{command_name}`

#### section_open
Opens a section/locker.
```json
{
    "section_id": 1
}
```
Response:
```json
{
    "success": true
}
```

#### check_doors
Checks door state for a section.
```json
{
    "section_id": 1
}
```
Response:
```json
{
    "door_state": "open"
}
```

### System Commands
Topic: `devices/{clientid}/system/{command_name}`

#### reboot_device
Reboots the device (placeholder implementation).
```json
{}
```
Response:
```json
{
    "success": true,
    "message": "Reboot command received (not implemented yet)"
}
```

### Sale Commands
Topic: `devices/{clientid}/sale/{command_name}`

#### edit_reservation
Edits a sale reservation.
```json
{
    "uuid": "aafdasfd34qq3g",
    "status": 0,
    "max_days": 1
}
```
Response:
```json
{
    "success": true
}
```

### Storage Commands
Topic: `devices/{clientid}/storage/{command_name}`

#### storage_edit_reservation
Edits a storage reservation.
```json
{
    "uuid": "aafdasfd34qq3g",
    "status": 0,
    "max_days": 1
}
```
Response:
```json
{
    "success": true
}
```

## Response Topics

Responses are published to topics with the following format:
- Request: `devices/{clientid}/{category}/{command}`
- Response: `devices/{clientid}/responses/{category}/{command}`

For example:
- Request topic: `devices/1234/electronic/section_open`
- Response topic: `devices/1234/responses/electronic/section_open`

## Architecture

### Files
- `client.py`: MQTT client implementation with connection management
- `handlers.py`: Command handlers for different categories
- `__init__.py`: Module exports

### Integration
The MQTT client is automatically started when the FastAPI application starts and stopped when it shuts down. It integrates with:

- Hardware control system for electronic commands
- Database for reservation management
- Timeline logging for audit trails

## Testing

Run the test script to verify MQTT functionality:

```bash
python test_mqtt.py
```

## Error Handling

All commands return appropriate error responses:
```json
{
    "success": false,
    "error": "Error description"
}
```

## Logging

The MQTT module uses Python's logging system with appropriate log levels:
- INFO: Connection status, successful operations
- ERROR: Connection failures, command errors
- DEBUG: Detailed message processing information
