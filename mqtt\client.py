"""
MQTT Client implementation for device communication
"""

import asyncio
import json
import logging
from typing import Optional
import paho.mqtt.client as mqtt
from os import getenv
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

class AsyncMQTTClient:
    def __init__(self, broker_host: str, broker_port: int = None, username: str = None, password: str = None, client_id: str = None):
        if not broker_host:
            raise ValueError("broker_host is required")
        if not broker_port:
            broker_port = 1883  # Default MQTT port
        if not isinstance(broker_port, int):
            try:
                broker_port = int(broker_port)
            except (ValueError, TypeError):
                raise ValueError("broker_port must be an integer")
        
        self.broker_host = broker_host
        self.broker_port = broker_port
        self.username = username
        self.password = password
        self.client_id = client_id
        self.is_connected = False
        self.is_subscribed = False
        self.main_loop = None  # Store reference to main event loop
        self.subscription_mids = {}  # Track subscription message IDs
        
        self.client = mqtt.Client(client_id=client_id)
        
        # Set authentication if provided
        if username and password:
            self.client.username_pw_set(username, password)
            
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_disconnect = self.on_disconnect
        self.client.on_subscribe = self.on_subscribe
        self.messages_queue = asyncio.Queue()
        
    def set_main_loop(self, loop):
        """Set the main event loop reference"""
        self.main_loop = loop
        logger.info("Main event loop reference set")
        
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            self.is_connected = True
            logger.info(f"Connected to MQTT broker successfully")
            logger.info(f"   Broker: {self.broker_host}:{self.broker_port}")
            logger.info(f"   Client ID: {self.client_id}")
            logger.info(f"   Username: {self.username}")
            
            # Subscribe to all command topics for this client
            logger.info("Subscribing to command topics...")
            
            # Electronic commands
            topic_electronic = f"devices/{self.client_id}/electronic/#"
            mid1 = client.subscribe(topic_electronic)
            self.subscription_mids[mid1[1]] = topic_electronic
            logger.info(f"   Message ID {mid1[1]}: {topic_electronic}")
            
            # System commands
            topic_system = f"devices/{self.client_id}/system/#"
            mid2 = client.subscribe(topic_system)
            self.subscription_mids[mid2[1]] = topic_system
            logger.info(f"   Message ID {mid2[1]}: {topic_system}")
            
            # Sale commands
            topic_sale = f"devices/{self.client_id}/sale/#"
            mid3 = client.subscribe(topic_sale)
            self.subscription_mids[mid3[1]] = topic_sale
            logger.info(f"   Message ID {mid3[1]}: {topic_sale}")
            
            # Storage commands
            topic_storage = f"devices/{self.client_id}/storage/#"
            mid4 = client.subscribe(topic_storage)
            self.subscription_mids[mid4[1]] = topic_storage
            logger.info(f"   Message ID {mid4[1]}: {topic_storage}")
            
        else:
            self.is_connected = False
            logger.error(f"Failed to connect to MQTT broker with result code {rc}")
            
    def on_subscribe(self, client, userdata, mid, granted_qos):
        topic = self.subscription_mids.get(mid, f"Unknown topic (MID: {mid})")
        logger.info(f"Subscription callback received:")
        logger.info(f"   Message ID: {mid}")
        logger.info(f"   Topic: {topic}")
        logger.info(f"   Granted QoS: {granted_qos}")
        
        # Check if subscription was successful
        if granted_qos and len(granted_qos) > 0:
            qos = granted_qos[0]
            if qos == 128:
                logger.error(f"Subscription FAILED for: {topic}")
                logger.error(f"   QoS 128 indicates failure - topic pattern invalid or access denied")
            elif qos >= 0 and qos <= 2:
                logger.info(f"Subscription SUCCESS for: {topic}")
                logger.info(f"   QoS: {qos} - topic is now active and listening")
                # Mark as subscribed if ANY subscription succeeds
                if not self.is_subscribed:
                    self.is_subscribed = True
                    logger.info("First successful subscription - client is now ready!")
            else:
                logger.warning(f"Subscription UNCLEAR for: {topic}")
                logger.warning(f"   QoS: {qos} - unexpected value")
        else:
            logger.error(f"Subscription FAILED for: {topic}")
            logger.error(f"   No QoS granted")
        
        # Check if we have at least one successful subscription
        if self.is_subscribed:
            logger.info("At least one topic subscription is active")
            logger.info("MQTT client is fully ready and listening for messages")
        else:
            logger.info("Still waiting for successful subscription...")
            logger.info(f"   Current status - Connected: {self.is_connected}, Subscribed: {self.is_subscribed}")
            
    def on_disconnect(self, client, userdata, rc):
        self.is_connected = False
        self.is_subscribed = False
        logger.warning(f"Disconnected from MQTT broker (rc={rc})")
        
    def on_message(self, client, userdata, msg):
        logger.debug("Raw MQTT message received")
        logger.debug(f"   Connected: {self.is_connected}")
        logger.debug(f"   Subscribed: {self.is_subscribed}")
        
        # Handle topic display safely
        if isinstance(msg.topic, bytes):
            topic_display = msg.topic.decode()
        else:
            topic_display = msg.topic
        logger.debug(f"   Topic: {topic_display}")
        
        if self.is_connected and self.is_subscribed:
            logger.debug("Processing message (fully ready)")
            # Use the stored main loop reference to schedule the coroutine
            if self.main_loop and self.main_loop.is_running():
                try:
                    future = asyncio.run_coroutine_threadsafe(
                        self.process_message(msg), self.main_loop
                    )
                    logger.debug("Message processing scheduled in main event loop")
                except Exception as e:
                    logger.error(f"Error scheduling message processing: {e}")
            else:
                logger.error("Main loop not available, cannot process message")
        else:
            logger.debug("Message received but not fully ready yet")
            # Queue message for later processing
            if self.main_loop and self.main_loop.is_running():
                try:
                    asyncio.run_coroutine_threadsafe(
                        self.queue_message_async(msg), self.main_loop
                    )
                    logger.debug("Message queued for later processing")
                except Exception as e:
                    logger.error(f"Error queuing message: {e}")
            else:
                logger.error("Main loop not available, cannot queue message")

    async def process_message(self, msg):
        try:
            # Handle topic - check if it's already a string or needs decoding
            if isinstance(msg.topic, bytes):
                topic = msg.topic.decode()
            else:
                topic = msg.topic

            # Handle payload - check if it's already a string or needs decoding
            if isinstance(msg.payload, bytes):
                payload = msg.payload.decode()
            else:
                payload = msg.payload if msg.payload else ""

            logger.info(f"MQTT Message received:")
            logger.info(f"   Topic: {topic}")
            logger.info(f"   Payload: {payload}")
            logger.info(f"   QoS: {msg.qos}")
            logger.info(f"   Retain: {msg.retain}")

            # Import handler here to avoid circular imports
            from .handlers import MQTTCommandHandler
            handler = MQTTCommandHandler()

            # Process the message and get response
            response = await handler.handle_message(topic, payload)

            if response:
                # Publish response back to MQTT
                await self.publish_response(topic, response)

        except Exception as e:
            logger.error(f"Error processing message: {e}")
            logger.error(f"   Topic type: {type(msg.topic)}, value: {msg.topic}")
            logger.error(f"   Payload type: {type(msg.payload)}, value: {msg.payload}")

    async def queue_message_async(self, msg):
        """Async helper to actually queue the message"""
        await self.messages_queue.put(msg)
        logger.debug(f"Message queued: {msg.topic}")

    async def process_queued_messages(self):
        """Process any messages that arrived before we were fully ready"""
        if not self.messages_queue.empty():
            logger.info(f"Processing {self.messages_queue.qsize()} queued messages...")
            while not self.messages_queue.empty():
                msg = await self.messages_queue.get()
                logger.info(f"Processing queued message: {msg.topic}")
                await self.process_message(msg)

    async def start(self):
        logger.info(f"Connecting to MQTT broker: {self.broker_host}:{self.broker_port}")
        logger.info(f"   Client ID: {self.client_id}")
        logger.info(f"   Username: {self.username}")

        self.client.connect(self.broker_host, self.broker_port, 60)
        self.client.loop_start()

        # Wait for connection and at least one subscription to be established
        logger.info("Waiting for connection and subscription...")
        timeout = 15  # 15 seconds timeout
        start_time = asyncio.get_event_loop().time()

        while not (self.is_connected and self.is_subscribed):
            current_time = asyncio.get_event_loop().time()
            elapsed = current_time - start_time

            if elapsed > timeout:
                logger.warning(f"Timeout waiting for full connection after {elapsed:.1f}s")
                break

            logger.debug(f"Waiting... ({elapsed:.1f}s elapsed)")
            logger.debug(f"   Connected: {self.is_connected}")
            logger.debug(f"   Subscribed: {self.is_subscribed}")

            await asyncio.sleep(0.5)

        if self.is_connected and self.is_subscribed:
            logger.info("MQTT client is fully ready and listening for messages")
            # Process any messages that arrived before we were ready
            await self.process_queued_messages()
        elif self.is_connected:
            logger.info("Connected but subscription status unclear")
            logger.info(f"   Connected: {self.is_connected}")
            logger.info(f"   Subscribed: {self.is_subscribed}")
            logger.info("   Continuing anyway - messages may still be received...")
            # Process any messages that arrived before we were ready
            await self.process_queued_messages()
        else:
            logger.error("MQTT client failed to connect properly")
            logger.error(f"   Connected: {self.is_connected}")
            logger.error(f"   Subscribed: {self.is_subscribed}")

    async def stop(self):
        self.client.loop_stop()
        self.client.disconnect()

    async def publish_response(self, request_topic: str, response: dict):
        """Publish response back to MQTT topic"""
        if self.is_connected:
            # Convert request topic to response topic
            # devices/{clientid}/electronic/{command} -> devices/{clientid}/responses/electronic/{command}
            # devices/{clientid}/system/{command} -> devices/{clientid}/responses/system/{command}
            # etc.
            parts = request_topic.split("/")
            if len(parts) >= 4:
                # Insert 'responses' after clientid
                response_topic = f"{parts[0]}/{parts[1]}/responses/{parts[2]}/{parts[3]}"
                response_payload = json.dumps(response, ensure_ascii=False)

                try:
                    info = self.client.publish(response_topic, response_payload, qos=1, retain=False)
                    if info.rc == 0:
                        # Wait for PUBACK when QoS=1 to confirm broker accepted it
                        info.wait_for_publish()
                        logger.info(f"Response published to: {response_topic}")
                        logger.debug(f"   Payload: {response_payload}")
                        logger.debug(f"   mid={getattr(info, 'mid', '?')} (acknowledged)")
                    else:
                        logger.error(f"Failed to publish response (client-side rc): {info.rc}")
                except Exception as e:
                    logger.error(f"Error publishing response: {e}")
            else:
                logger.error(f"Invalid topic format for response: {request_topic}")
        else:
            logger.error("Cannot publish response - not connected")


# Global MQTT client instance
mqtt_client: Optional[AsyncMQTTClient] = None


async def mqtt_listener():
    """Main MQTT listener function"""
    global mqtt_client

    # Get the current event loop
    main_loop = asyncio.get_running_loop()

    # Validate required environment variables
    host = getenv("MQTT_HOST")
    username = getenv("MQTT_USERNAME")
    password = getenv("MQTT_PASSWORD")
    client_id = getenv("MQTT_CLIENT_ID")

    if not host:
        raise ValueError("Environment variable 'MQTT_HOST' is required in .env file")
    if not username:
        raise ValueError("Environment variable 'MQTT_USERNAME' is required in .env file")
    if not password:
        raise ValueError("Environment variable 'MQTT_PASSWORD' is required in .env file")
    if not client_id:
        raise ValueError("Environment variable 'MQTT_CLIENT_ID' is required in .env file")

    mqtt_client = AsyncMQTTClient(
        broker_host=host,
        broker_port=int(getenv("MQTT_PORT")) if getenv("MQTT_PORT") else None,
        username=username,
        password=password,
        client_id=client_id
    )

    # Set the main event loop reference
    mqtt_client.set_main_loop(main_loop)

    await mqtt_client.start()

    # Keep the task running
    try:
        while True:
            await asyncio.sleep(1)
    except asyncio.CancelledError:
        if mqtt_client:
            await mqtt_client.stop()
