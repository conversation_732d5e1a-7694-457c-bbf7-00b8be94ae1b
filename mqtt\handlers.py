"""
MQTT Command Handlers for different device commands
"""

import json
import logging
from typing import Dict, Any, Optional
import mysql.connector
from os import getenv
from managers.timeline_logger import log_timeline_event

logger = logging.getLogger(__name__)


class MQTTCommandHandler:
    """Handles MQTT commands for different device categories"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def _get_db_connection(self):
        """Get database connection"""
        return mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
    
    async def handle_message(self, topic: str, payload: str) -> Optional[Dict[str, Any]]:
        """
        Handle incoming MQTT message and route to appropriate handler
        
        Args:
            topic: MQTT topic (e.g., devices/{clientid}/electronic/section_open)
            payload: JSON payload as string
            
        Returns:
            Response dictionary or None
        """
        try:
            # Parse topic
            parts = topic.split("/")
            if len(parts) < 4:
                logger.error(f"Invalid topic format: {topic}")
                return {"success": False, "error": "Invalid topic format"}
            
            clientid = parts[1]
            category = parts[2]  # electronic, system, sale, storage
            command = parts[3]
            
            # Parse payload
            try:
                data = json.loads(payload) if payload else {}
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON payload: {payload}, error: {e}")
                return {"success": False, "error": "Invalid JSON payload"}
            
            logger.info(f"Processing command: {command} for category: {category} from client: {clientid}")
            logger.debug(f"Data: {data}")
            
            # Route to appropriate handler
            if category == "electronic":
                return await self._handle_electronic_command(clientid, command, data)
            elif category == "system":
                return await self._handle_system_command(clientid, command, data)
            elif category == "sale":
                return await self._handle_sale_command(clientid, command, data)
            elif category == "storage":
                return await self._handle_storage_command(clientid, command, data)
            else:
                logger.error(f"Unknown command category: {category}")
                return {"success": False, "error": f"Unknown command category: {category}"}
                
        except Exception as e:
            logger.error(f"Error handling MQTT message: {e}")
            return {"success": False, "error": f"Internal error: {str(e)}"}
    
    async def _handle_electronic_command(self, clientid: str, command: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle electronic commands"""
        try:
            if command == "section_open":
                return await self._section_open(clientid, data)
            elif command == "check_doors":
                return await self._check_doors(clientid, data)
            else:
                logger.error(f"Unknown electronic command: {command}")
                return {"success": False, "error": f"Unknown electronic command: {command}"}
                
        except Exception as e:
            logger.error(f"Error handling electronic command {command}: {e}")
            return {"success": False, "error": f"Electronic command error: {str(e)}"}
    
    async def _handle_system_command(self, clientid: str, command: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle system commands"""
        try:
            if command == "reboot_device":
                return await self._reboot_device(clientid, data)
            else:
                logger.error(f"Unknown system command: {command}")
                return {"success": False, "error": f"Unknown system command: {command}"}
                
        except Exception as e:
            logger.error(f"Error handling system command {command}: {e}")
            return {"success": False, "error": f"System command error: {str(e)}"}
    
    async def _handle_sale_command(self, clientid: str, command: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle sale commands"""
        try:
            if command == "edit_reservation":
                return await self._edit_sale_reservation(clientid, data)
            else:
                logger.error(f"Unknown sale command: {command}")
                return {"success": False, "error": f"Unknown sale command: {command}"}
                
        except Exception as e:
            logger.error(f"Error handling sale command {command}: {e}")
            return {"success": False, "error": f"Sale command error: {str(e)}"}
    
    async def _handle_storage_command(self, clientid: str, command: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle storage commands"""
        try:
            if command == "storage_edit_reservation":
                return await self._edit_storage_reservation(clientid, data)
            else:
                logger.error(f"Unknown storage command: {command}")
                return {"success": False, "error": f"Unknown storage command: {command}"}
                
        except Exception as e:
            logger.error(f"Error handling storage command {command}: {e}")
            return {"success": False, "error": f"Storage command error: {str(e)}"}
    
    async def _section_open(self, clientid: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle section_open command"""
        try:
            section_id = data.get("section_id")
            if section_id is None:
                return {"success": False, "error": "section_id is required"}
            
            # Import hardware controller
            from hardware.locker_control import LockerController
            
            # Get section info to determine if it's tempered
            from infrastructure.repositories.section_repository import SectionRepository
            section_repo = SectionRepository()
            section_info = section_repo.get_section_by_id(section_id)
            
            is_tempered = bool(section_info.get("tempered", 1))
            
            # Initialize hardware controller and unlock the section
            controller = LockerController()
            success = await controller.unlock_locker(
                locker_id=section_id,
                is_tempered=is_tempered,
                mode="mqtt"
            )
            
            if success:
                logger.info(f"Successfully opened section {section_id} via MQTT")
                return {"success": True}
            else:
                logger.error(f"Failed to open section {section_id} via MQTT")
                return {"success": False, "error": "Failed to open section"}
                
        except Exception as e:
            logger.error(f"Error in section_open: {e}")
            return {"success": False, "error": f"Section open error: {str(e)}"}
    
    async def _check_doors(self, clientid: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle check_doors command"""
        try:
            section_id = data.get("section_id")
            if section_id is None:
                return {"success": False, "error": "section_id is required"}
            
            # Import hardware API
            from hardware.electronics_api import send_command
            
            # Check door state
            result = await send_command(str(section_id), "check_door")
            
            # Parse result - typically "1" for closed, "0" for open, or error codes starting with "-"
            if result.startswith('-'):
                logger.error(f"Error checking door state for section {section_id}: {result}")
                return {"success": False, "error": f"Hardware error: {result}"}
            
            # Convert result to door state
            door_state = "closed" if result == "1" else "open"
            
            logger.info(f"Door state for section {section_id}: {door_state}")
            return {"door_state": door_state}
            
        except Exception as e:
            logger.error(f"Error in check_doors: {e}")
            return {"success": False, "error": f"Check doors error: {str(e)}"}
    
    async def _reboot_device(self, clientid: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle reboot_device command - placeholder for future implementation"""
        try:
            logger.info(f"Reboot device command received for client {clientid}")
            # TODO: Implement device reboot functionality
            # This is a placeholder as requested
            return {"success": True, "message": "Reboot command received (not implemented yet)"}
            
        except Exception as e:
            logger.error(f"Error in reboot_device: {e}")
            return {"success": False, "error": f"Reboot device error: {str(e)}"}
    
    async def _edit_sale_reservation(self, clientid: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle edit_reservation command for sale reservations"""
        try:
            uuid = data.get("uuid")
            status = data.get("status")
            max_days = data.get("max_days")
            
            if uuid is None:
                return {"success": False, "error": "uuid is required"}
            if status is None:
                return {"success": False, "error": "status is required"}
            if max_days is None:
                return {"success": False, "error": "max_days is required"}
            
            # Update sale reservation
            conn = self._get_db_connection()
            cursor = conn.cursor()
            
            try:
                # Check if reservation exists
                cursor.execute("SELECT id FROM sale_reservations WHERE uuid = %s", (uuid,))
                result = cursor.fetchone()
                
                if not result:
                    return {"success": False, "error": "Reservation not found"}
                
                # Update reservation
                cursor.execute("""
                    UPDATE sale_reservations 
                    SET status = %s, max_days = %s, last_update = NOW()
                    WHERE uuid = %s
                """, (status, max_days, uuid))
                
                conn.commit()
                
                logger.info(f"Updated sale reservation {uuid}: status={status}, max_days={max_days}")
                
                # Log timeline event
                log_timeline_event(
                    event_type="edit_reservation",
                    event_result="success",
                    message=f"MQTT: Updated sale reservation {uuid}",
                    mode="sale"
                )
                
                return {"success": True}
                
            finally:
                cursor.close()
                conn.close()
                
        except Exception as e:
            logger.error(f"Error in edit_sale_reservation: {e}")
            return {"success": False, "error": f"Edit sale reservation error: {str(e)}"}
    
    async def _edit_storage_reservation(self, clientid: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle storage_edit_reservation command for storage reservations"""
        try:
            uuid = data.get("uuid")
            status = data.get("status")
            max_days = data.get("max_days")
            
            if uuid is None:
                return {"success": False, "error": "uuid is required"}
            if status is None:
                return {"success": False, "error": "status is required"}
            if max_days is None:
                return {"success": False, "error": "max_days is required"}
            
            # Update storage reservation
            conn = self._get_db_connection()
            cursor = conn.cursor()
            
            try:
                # Check if reservation exists
                cursor.execute("SELECT id FROM storage_reservations WHERE uuid = %s", (uuid,))
                result = cursor.fetchone()
                
                if not result:
                    return {"success": False, "error": "Reservation not found"}
                
                # Update reservation
                cursor.execute("""
                    UPDATE storage_reservations 
                    SET status = %s, max_days = %s, last_update = NOW()
                    WHERE uuid = %s
                """, (status, max_days, uuid))
                
                conn.commit()
                
                logger.info(f"Updated storage reservation {uuid}: status={status}, max_days={max_days}")
                
                # Log timeline event
                log_timeline_event(
                    event_type="edit_reservation",
                    event_result="success",
                    message=f"MQTT: Updated storage reservation {uuid}",
                    mode="storage"
                )
                
                return {"success": True}
                
            finally:
                cursor.close()
                conn.close()
                
        except Exception as e:
            logger.error(f"Error in edit_storage_reservation: {e}")
            return {"success": False, "error": f"Edit storage reservation error: {str(e)}"}
