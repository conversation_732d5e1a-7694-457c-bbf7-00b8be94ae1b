#!/usr/bin/env python3
"""
Test script for MQTT functionality
"""

import asyncio
import json
import logging
from mqtt.client import Async<PERSON>QTT<PERSON>lient
from os import getenv
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(name)s: %(message)s')
logger = logging.getLogger(__name__)

async def test_mqtt_client():
    """Test MQTT client functionality"""
    
    # Get MQTT configuration
    host = getenv("MQTT_HOST")
    port = int(getenv("MQTT_PORT", "1883"))
    username = getenv("MQTT_USERNAME")
    password = getenv("MQTT_PASSWORD")
    client_id = getenv("MQTT_CLIENT_ID")
    
    if not all([host, username, password, client_id]):
        logger.error("Missing MQTT configuration in .env file")
        return
    
    logger.info(f"Testing MQTT connection to {host}:{port}")
    logger.info(f"Client ID: {client_id}")
    logger.info(f"Username: {username}")
    
    # Create MQTT client
    mqtt_client = AsyncMQTTClient(
        broker_host=host,
        broker_port=port,
        username=username,
        password=password,
        client_id=client_id
    )
    
    # Set main loop reference
    mqtt_client.set_main_loop(asyncio.get_running_loop())
    
    try:
        # Start client
        await mqtt_client.start()
        
        # Wait a bit to ensure connection is established
        await asyncio.sleep(2)
        
        if mqtt_client.is_connected and mqtt_client.is_subscribed:
            logger.info("✅ MQTT client connected and subscribed successfully!")
            
            # Test publishing a response (simulating a command response)
            test_response = {"success": True, "test": "mqtt_functionality"}
            test_topic = f"devices/{client_id}/electronic/section_open"
            
            await mqtt_client.publish_response(test_topic, test_response)
            logger.info("✅ Test response published successfully!")
            
            # Keep running for a bit to receive any messages
            logger.info("Listening for messages for 10 seconds...")
            await asyncio.sleep(10)
            
        else:
            logger.error("❌ MQTT client failed to connect or subscribe")
            logger.error(f"Connected: {mqtt_client.is_connected}")
            logger.error(f"Subscribed: {mqtt_client.is_subscribed}")
            
    except Exception as e:
        logger.error(f"❌ Error testing MQTT client: {e}")
        
    finally:
        # Stop client
        await mqtt_client.stop()
        logger.info("MQTT client stopped")

if __name__ == "__main__":
    asyncio.run(test_mqtt_client())
