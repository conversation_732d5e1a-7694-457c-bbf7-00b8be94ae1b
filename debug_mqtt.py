#!/usr/bin/env python3
"""
Debug MQTT functionality - check if everything is working
"""

import asyncio
import logging
from mqtt.client import mqtt_listener, AsyncMQTTClient
from os import getenv
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup detailed logging
logging.basicConfig(
    level=logging.DEBUG, 
    format='%(asctime)s %(levelname)s %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)

async def debug_mqtt():
    """Debug MQTT functionality"""
    
    logger.info("=== MQTT Debug Session ===")
    
    # Check environment variables
    logger.info("Checking environment variables...")
    host = getenv("MQTT_HOST")
    port = getenv("MQTT_PORT")
    username = getenv("MQTT_USERNAME")
    password = getenv("MQTT_PASSWORD")
    client_id = getenv("MQTT_CLIENT_ID")
    
    logger.info(f"MQTT_HOST: {host}")
    logger.info(f"MQTT_PORT: {port}")
    logger.info(f"MQTT_USERNAME: {username}")
    logger.info(f"MQTT_PASSWORD: {'***' if password else 'None'}")
    logger.info(f"MQTT_CLIENT_ID: {client_id}")
    
    if not all([host, username, password, client_id]):
        logger.error("❌ Missing required MQTT environment variables!")
        return
    
    logger.info("✅ All MQTT environment variables are set")
    
    # Test direct MQTT client creation
    logger.info("\nTesting direct MQTT client creation...")
    try:
        mqtt_client = AsyncMQTTClient(
            broker_host=host,
            broker_port=int(port) if port else 1883,
            username=username,
            password=password,
            client_id=client_id
        )
        
        # Set main loop reference
        mqtt_client.set_main_loop(asyncio.get_running_loop())
        
        logger.info("✅ MQTT client created successfully")
        
        # Try to start the client
        logger.info("Starting MQTT client...")
        await mqtt_client.start()
        
        if mqtt_client.is_connected:
            logger.info("✅ MQTT client connected successfully!")
            
            if mqtt_client.is_subscribed:
                logger.info("✅ MQTT client subscribed to topics successfully!")
                
                # Keep running for a bit to test message handling
                logger.info("Listening for messages for 30 seconds...")
                logger.info("You can now send test messages to:")
                logger.info(f"  - devices/{client_id}/sale/edit_reservation")
                logger.info(f"  - devices/{client_id}/electronic/section_open")
                logger.info(f"  - devices/{client_id}/system/reboot_device")
                logger.info(f"  - devices/{client_id}/storage/storage_edit_reservation")
                
                await asyncio.sleep(30)
                
            else:
                logger.error("❌ MQTT client failed to subscribe to topics")
        else:
            logger.error("❌ MQTT client failed to connect")
            
        # Stop client
        await mqtt_client.stop()
        logger.info("MQTT client stopped")
        
    except Exception as e:
        logger.error(f"❌ Error testing MQTT client: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_mqtt())
