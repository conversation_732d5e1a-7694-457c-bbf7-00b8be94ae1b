#!/usr/bin/env python3
"""
Test the specific sale/edit_reservation command you mentioned
"""

import json
import time
import paho.mqtt.client as mqtt
from os import getenv
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def on_connect(client, userdata, flags, rc):
    if rc == 0:
        print("✅ Connected to MQTT broker")
        # Subscribe to response topics
        client_id = getenv("MQTT_CLIENT_ID")
        response_topic = f"devices/{client_id}/responses/#"
        client.subscribe(response_topic)
        print(f"📡 Subscribed to response topic: {response_topic}")
    else:
        print(f"❌ Failed to connect with result code {rc}")

def on_message(client, userdata, msg):
    topic = msg.topic.decode() if isinstance(msg.topic, bytes) else msg.topic
    payload = msg.payload.decode() if isinstance(msg.payload, bytes) else msg.payload
    
    print(f"\n📨 Response received:")
    print(f"   Topic: {topic}")
    print(f"   Payload: {payload}")
    
    try:
        response_data = json.loads(payload)
        print(f"   Parsed: {json.dumps(response_data, indent=2)}")
    except json.JSONDecodeError:
        print(f"   Raw payload: {payload}")

def test_sale_edit_reservation():
    """Test the exact command you mentioned"""
    
    # Get MQTT configuration
    host = getenv("MQTT_HOST")
    port = int(getenv("MQTT_PORT", "1883"))
    username = getenv("MQTT_USERNAME")
    password = getenv("MQTT_PASSWORD")
    client_id = getenv("MQTT_CLIENT_ID")
    
    if not all([host, username, password, client_id]):
        print("❌ Missing MQTT configuration in .env file")
        return
    
    print(f"🔗 Connecting to MQTT broker: {host}:{port}")
    print(f"   Client ID: {client_id}")
    print(f"   Username: {username}")
    
    # Create MQTT client
    client = mqtt.Client(client_id=f"{client_id}_test_publisher")
    client.username_pw_set(username, password)
    client.on_connect = on_connect
    client.on_message = on_message
    
    try:
        # Connect to broker
        client.connect(host, port, 60)
        client.loop_start()
        
        # Wait for connection
        time.sleep(2)
        
        # Test the exact command you mentioned
        command_topic = f"devices/{client_id}/sale/edit_reservation"
        test_payload = {
            "uuid": "test-uuid-12345",
            "status": 0,
            "max_days": 7
        }
        
        print(f"\n🚀 Sending test command:")
        print(f"   Topic: {command_topic}")
        print(f"   Payload: {json.dumps(test_payload, indent=2)}")
        
        # Publish command
        result = client.publish(command_topic, json.dumps(test_payload), qos=1)
        
        if result.rc == 0:
            print("   ✅ Command sent successfully")
        else:
            print(f"   ❌ Failed to send command (rc: {result.rc})")
        
        print(f"\n⏳ Waiting for response on devices/{client_id}/responses/# ...")
        print("   (waiting 15 seconds)")
        time.sleep(15)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        
    finally:
        client.loop_stop()
        client.disconnect()
        print("\n🔌 Disconnected from MQTT broker")

if __name__ == "__main__":
    test_sale_edit_reservation()
