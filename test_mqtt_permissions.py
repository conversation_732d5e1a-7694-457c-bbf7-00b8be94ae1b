#!/usr/bin/env python3
"""
Test MQTT broker permissions and topic patterns
"""

import paho.mqtt.client as mqtt
import time
import json
from os import getenv
from dotenv import load_dotenv

load_dotenv()

def on_connect(client, userdata, flags, rc):
    if rc == 0:
        print("✅ Connected to MQTT broker")
        
        # Test different subscription patterns
        test_topics = [
            "devices/1234/sale/edit_reservation",  # Specific topic
            "devices/1234/sale/#",                 # Wildcard
            "devices/1234/#",                      # Broader wildcard
            "devices/#",                           # Even broader
            "#",                                   # All topics
            "devices/1234/responses/#",            # Response topics
        ]
        
        for topic in test_topics:
            print(f"🔍 Testing subscription to: {topic}")
            result = client.subscribe(topic)
            print(f"   Subscribe result: {result}")
            time.sleep(0.5)
            
    else:
        print(f"❌ Failed to connect with result code {rc}")

def on_subscribe(client, userdata, mid, granted_qos):
    print(f"📡 Subscription callback - MID: {mid}, QoS: {granted_qos}")
    if granted_qos[0] == 128:
        print("   ❌ Subscription FAILED (QoS 128 - access denied)")
    else:
        print(f"   ✅ Subscription SUCCESS (QoS {granted_qos[0]})")

def on_message(client, userdata, msg):
    topic = msg.topic.decode() if isinstance(msg.topic, bytes) else msg.topic
    payload = msg.payload.decode() if isinstance(msg.payload, bytes) else msg.payload
    
    print(f"\n📨 Message received:")
    print(f"   Topic: {topic}")
    print(f"   Payload: {payload}")

def test_mqtt_permissions():
    """Test MQTT broker permissions"""
    
    host = getenv("MQTT_HOST")
    port = int(getenv("MQTT_PORT", "1883"))
    username = getenv("MQTT_USERNAME")
    password = getenv("MQTT_PASSWORD")
    client_id = getenv("MQTT_CLIENT_ID")
    
    print(f"🔗 Testing MQTT permissions on {host}:{port}")
    print(f"   Client ID: {client_id}")
    print(f"   Username: {username}")
    
    # Create MQTT client
    client = mqtt.Client(client_id=f"{client_id}_permission_test")
    client.username_pw_set(username, password)
    client.on_connect = on_connect
    client.on_subscribe = on_subscribe
    client.on_message = on_message
    
    try:
        # Connect to broker
        client.connect(host, port, 60)
        client.loop_start()
        
        # Wait for subscriptions to complete
        time.sleep(5)
        
        # Try to publish a test message to see if we can at least publish
        print(f"\n🚀 Testing publish capability...")
        test_topics = [
            f"devices/{client_id}/sale/edit_reservation",
            f"devices/{client_id}/responses/sale/edit_reservation",
        ]
        
        for topic in test_topics:
            test_payload = {"test": "message", "timestamp": time.time()}
            print(f"📤 Publishing to: {topic}")
            result = client.publish(topic, json.dumps(test_payload), qos=1)
            print(f"   Publish result: rc={result.rc}, mid={result.mid}")
            time.sleep(1)
        
        # Wait for any messages
        print(f"\n⏳ Waiting for messages (10 seconds)...")
        time.sleep(10)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        
    finally:
        client.loop_stop()
        client.disconnect()
        print("\n🔌 Disconnected from MQTT broker")

if __name__ == "__main__":
    test_mqtt_permissions()
