#!/usr/bin/env python3
"""
MQTT Publisher Test Script
Use this to send test commands to the MQTT broker
"""

import json
import time
import paho.mqtt.client as mqtt
from os import getenv
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def on_connect(client, userdata, flags, rc):
    if rc == 0:
        print("✅ Connected to MQTT broker")
        # Subscribe to response topics to see responses
        client_id = getenv("MQTT_CLIENT_ID")
        response_topic = f"devices/{client_id}/responses/#"
        client.subscribe(response_topic)
        print(f"📡 Subscribed to response topic: {response_topic}")
    else:
        print(f"❌ Failed to connect with result code {rc}")

def on_message(client, userdata, msg):
    topic = msg.topic.decode() if isinstance(msg.topic, bytes) else msg.topic
    payload = msg.payload.decode() if isinstance(msg.payload, bytes) else msg.payload
    
    print(f"\n📨 Response received:")
    print(f"   Topic: {topic}")
    print(f"   Payload: {payload}")
    
    try:
        response_data = json.loads(payload)
        print(f"   Parsed: {json.dumps(response_data, indent=2)}")
    except json.JSONDecodeError:
        print(f"   Raw payload: {payload}")

def send_test_commands():
    """Send test commands to the MQTT broker"""
    
    # Get MQTT configuration
    host = getenv("MQTT_HOST")
    port = int(getenv("MQTT_PORT", "1883"))
    username = getenv("MQTT_USERNAME")
    password = getenv("MQTT_PASSWORD")
    client_id = getenv("MQTT_CLIENT_ID")
    
    if not all([host, username, password, client_id]):
        print("❌ Missing MQTT configuration in .env file")
        return
    
    print(f"🔗 Connecting to MQTT broker: {host}:{port}")
    print(f"   Client ID: {client_id}")
    print(f"   Username: {username}")
    
    # Create MQTT client
    client = mqtt.Client(client_id=f"{client_id}_publisher")
    client.username_pw_set(username, password)
    client.on_connect = on_connect
    client.on_message = on_message
    
    try:
        # Connect to broker
        client.connect(host, port, 60)
        client.loop_start()
        
        # Wait for connection
        time.sleep(2)
        
        # Test commands
        test_commands = [
            {
                "topic": f"devices/{client_id}/electronic/section_open",
                "payload": {"section_id": 1},
                "description": "Open section 1"
            },
            {
                "topic": f"devices/{client_id}/electronic/check_doors",
                "payload": {"section_id": 1},
                "description": "Check door state for section 1"
            },
            {
                "topic": f"devices/{client_id}/system/reboot_device",
                "payload": {},
                "description": "Reboot device"
            },
            {
                "topic": f"devices/{client_id}/sale/edit_reservation",
                "payload": {
                    "uuid": "test-uuid-123",
                    "status": 0,
                    "max_days": 5
                },
                "description": "Edit sale reservation"
            },
            {
                "topic": f"devices/{client_id}/storage/storage_edit_reservation",
                "payload": {
                    "uuid": "test-storage-uuid-456",
                    "status": 1,
                    "max_days": 3
                },
                "description": "Edit storage reservation"
            }
        ]
        
        print("\n🚀 Sending test commands...")
        
        for i, cmd in enumerate(test_commands, 1):
            print(f"\n📤 Command {i}: {cmd['description']}")
            print(f"   Topic: {cmd['topic']}")
            print(f"   Payload: {json.dumps(cmd['payload'])}")
            
            # Publish command
            result = client.publish(cmd['topic'], json.dumps(cmd['payload']), qos=1)
            
            if result.rc == 0:
                print("   ✅ Command sent successfully")
            else:
                print(f"   ❌ Failed to send command (rc: {result.rc})")
            
            # Wait between commands
            time.sleep(2)
        
        print("\n⏳ Waiting for responses (10 seconds)...")
        time.sleep(10)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        
    finally:
        client.loop_stop()
        client.disconnect()
        print("\n🔌 Disconnected from MQTT broker")

if __name__ == "__main__":
    send_test_commands()
