import asyncio
import os
from fastapi import FastAPI
import json
import paho.mqtt.client as mqtt
from typing import Optional
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv(override=True)

app = FastAPI()
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(name)s: %(message)s')
logger = logging.getLogger(__name__)

async def handle_command(clientid: str, command: str):
    logger.info("Executing command for %s: %s", clientid, command)
    
    response = {"success": "true", "clientid": clientid, "command": command}
    
    if command == "unlock":
        await asyncio.sleep(1)
        logger.info("Unlocked service.")
        response["message"] = "Service unlocked successfully"
    elif command == "unlock_service":
        await asyncio.sleep(1)
        logger.info("Unlocked service.")
        response["message"] = "Service unlocked successfully"
    elif command == "start":
        logger.info("Starting...")
        response["message"] = "Service started successfully"
    elif command == "stop":
        logger.info("Stopping...")
        response["message"] = "Service stopped successfully"
    else:
        logger.warning("Unknown command: %s", command)
        response["success"] = "false"
        response["error"] = f"Unknown command: {command}"
    
    logger.info("Response: %s", response)
    return response

class AsyncMQTTClient:
    def __init__(self, broker_host: str, broker_port: int = None, username: str = None, password: str = None, client_id: str = None):
        if not broker_host:
            raise ValueError("broker_host is required")
        if not broker_port:
            broker_port = 1883  # Default MQTT port
        if not isinstance(broker_port, int):
            try:
                broker_port = int(broker_port)
            except (ValueError, TypeError):
                raise ValueError("broker_port must be an integer")
        
        self.broker_host = broker_host
        self.broker_port = broker_port
        self.username = username
        self.password = password
        self.client_id = client_id
        self.is_connected = False
        self.is_subscribed = False
        self.main_loop = None  # Store reference to main event loop
        self.subscription_mids = {}  # Track subscription message IDs
        
        self.client = mqtt.Client(client_id=client_id)
        
        # Set authentication if provided
        if username and password:
            self.client.username_pw_set(username, password)
            
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_disconnect = self.on_disconnect
        self.client.on_subscribe = self.on_subscribe
        self.messages_queue = asyncio.Queue()
        
    def set_main_loop(self, loop):
        """Set the main event loop reference"""
        self.main_loop = loop
        print(f"Main event loop reference set")
        
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            self.is_connected = True
            print(f"Connected to MQTT broker successfully")
            print(f"   Broker: {self.broker_host}:{self.broker_port}")
            print(f"   Client ID: {self.client_id}")
            print(f"   Username: {self.username}")
            
            # Subscribe to command topics (this should work)
            print(f"Subscribing to command topics...")
            mid1 = client.subscribe("devices/1234/commands/electronic/#")
            self.subscription_mids[mid1[1]] = "devices/1234/commands/electronic/#"
            print(f"   Message ID {mid1[1]}: devices/1234/commands/electronic/#")
            
            # Note: Response topics are created automatically when we publish to them
            print(f"Response topics will be created automatically when publishing")
            
        else:
            self.is_connected = False
            print(f"Failed to connect to MQTT broker with result code {rc}")
            # RC codes: 0=Success, 1=Incorrect protocol, 2=Invalid client ID, 3=Server unavailable, 4=Bad username/password, 5=Not authorized
            
    def on_subscribe(self, client, userdata, mid, granted_qos):
        topic = self.subscription_mids.get(mid, f"Unknown topic (MID: {mid})")
        print(f"Subscription callback received:")
        print(f"   Message ID: {mid}")
        print(f"   Topic: {topic}")
        print(f"   Granted QoS: {granted_qos}")
        
        # Check if subscription was successful
        if granted_qos and len(granted_qos) > 0:
            qos = granted_qos[0]
            if qos == 128:
                print(f"Subscription FAILED for: {topic}")
                print(f"   QoS 128 indicates failure - topic pattern invalid or access denied")
            elif qos >= 0 and qos <= 2:
                print(f"Subscription SUCCESS for: {topic}")
                print(f"   QoS: {qos} - topic is now active and listening")
                # Mark as subscribed if ANY subscription succeeds
                if not self.is_subscribed:
                    self.is_subscribed = True
                    print(f"First successful subscription - client is now ready!")
            else:
                print(f"Subscription UNCLEAR for: {topic}")
                print(f"   QoS: {qos} - unexpected value")
        else:
            print(f"Subscription FAILED for: {topic}")
            print(f"   No QoS granted")
        
        # Check if we have at least one successful subscription
        if self.is_subscribed:
            print(f"At least one topic subscription is active")
            print(f"MQTT client is fully ready and listening for messages")
        else:
            print(f"Still waiting for successful subscription...")
            print(f"   Current status - Connected: {self.is_connected}, Subscribed: {self.is_subscribed}")
            
    def on_disconnect(self, client, userdata, rc):
        self.is_connected = False
        self.is_subscribed = False
        print(f"Disconnected from MQTT broker (rc={rc})")
        
    def on_message(self, client, userdata, msg):
        print(f"Raw MQTT message received")
        print(f"   Connected: {self.is_connected}")
        print(f"   Subscribed: {self.is_subscribed}")
        
        # Handle topic display safely
        if isinstance(msg.topic, bytes):
            topic_display = msg.topic.decode()
        else:
            topic_display = msg.topic
        print(f"   Topic: {topic_display}")
        
        if self.is_connected and self.is_subscribed:
            print(f"Processing message (fully ready)")
            # Use the stored main loop reference to schedule the coroutine
            if self.main_loop and self.main_loop.is_running():
                try:
                    future = asyncio.run_coroutine_threadsafe(
                        self.process_message(msg), self.main_loop
                    )
                    print(f"Message processing scheduled in main event loop")
                except Exception as e:
                    print(f"Error scheduling message processing: {e}")
            else:
                print(f"Main loop not available, cannot process message")
        else:
            print(f"Message received but not fully ready yet")
            print(f"   Status check:")
            print(f"     - Connected: {self.is_connected}")
            print(f"     - Subscribed: {self.is_subscribed}")
            print(f"     - Main loop available: {self.main_loop is not None}")
            if self.main_loop:
                print(f"     - Main loop running: {self.main_loop.is_running()}")
            
            # Queue message for later processing
            if self.main_loop and self.main_loop.is_running():
                try:
                    asyncio.run_coroutine_threadsafe(
                        self.queue_message_async(msg), self.main_loop
                    )
                    print(f"Message queued for later processing")
                except Exception as e:
                    print(f"Error queuing message: {e}")
            else:
                print(f"Main loop not available, cannot queue message")
        
    async def process_message(self, msg):
        try:
            # Handle topic - check if it's already a string or needs decoding
            if isinstance(msg.topic, bytes):
                topic = msg.topic.decode()
            else:
                topic = msg.topic
                
            # Handle payload - check if it's already a string or needs decoding
            if isinstance(msg.payload, bytes):
                payload = msg.payload.decode()
            else:
                payload = msg.payload if msg.payload else ""
            
            print(f"MQTT Message received:")
            print(f"   Topic: {topic}")
            print(f"   Payload: {payload}")
            print(f"   QoS: {msg.qos}")
            print(f"   Retain: {msg.retain}")
            print("---")
            
            parts = topic.split("/")
            # Check if this is a command message: devices/{clientid}/commands/electronic/{command}
            if len(parts) >= 5 and parts[2] == "commands" and parts[3] == "electronic":
                clientid = parts[1]
                command = parts[4]
                if command:
                    print(f"Processing command: {command} for client: {clientid}")
                    response = await handle_command(clientid, command)
                    
                    # Publish response back to MQTT (same topic)
                    await self.publish_response(clientid, command, response)
                else:
                    print(f"No command found in topic: {topic}")
            else:
                # This is some other type of message
                print(f"Received other message type: {topic}")
                print(f"   Parts: {parts}")
                print("   Expected format: devices/{clientid}/commands/electronic/{command}")
                print(f"   Not a command - ignoring")
        except Exception as e:
            print(f"Error processing message: {e}")
            print(f"   Topic type: {type(msg.topic)}, value: {msg.topic}")
            print(f"   Payload type: {type(msg.payload)}, value: {msg.payload}")
    
    def queue_message(self, msg):
        """Queue message for later processing when fully ready"""
        if self.main_loop and self.main_loop.is_running():
            try:
                asyncio.run_coroutine_threadsafe(
                    self.queue_message_async(msg), self.main_loop
                )
                print(f"Message queued: {msg.topic}")
            except Exception as e:
                print(f"Error queuing message: {e}")
        else:
            print(f"Main loop not available, cannot queue message")
            
    async def queue_message_async(self, msg):
        """Async helper to actually queue the message"""
        await self.messages_queue.put(msg)
        print(f"Message queued: {msg.topic}")
        
    async def process_queued_messages(self):
        """Process any messages that arrived before we were fully ready"""
        if not self.messages_queue.empty():
            print(f"Processing {self.messages_queue.qsize()} queued messages...")
            while not self.messages_queue.empty():
                msg = await self.messages_queue.get()
                print(f"Processing queued message: {msg.topic}")
                await self.process_message(msg)
        
    async def start(self):
        print(f"Connecting to MQTT broker: {self.broker_host}:{self.broker_port}")
        print(f"   Client ID: {self.client_id}")
        print(f"   Username: {self.username}")
        
        self.client.connect(self.broker_host, self.broker_port, 60)
        self.client.loop_start()
        
        # Wait for connection and at least one subscription to be established
        print(f"Waiting for connection and subscription...")
        timeout = 15  # 15 seconds timeout
        start_time = asyncio.get_event_loop().time()
        
        while not (self.is_connected and self.is_subscribed):
            current_time = asyncio.get_event_loop().time()
            elapsed = current_time - start_time
            
            if elapsed > timeout:
                print(f"Timeout waiting for full connection after {elapsed:.1f}s")
                break
                
            print(f"Waiting... ({elapsed:.1f}s elapsed)")
            print(f"   Connected: {self.is_connected}")
            print(f"   Subscribed: {self.is_subscribed}")
            
            await asyncio.sleep(0.5)
        
        if self.is_connected and self.is_subscribed:
            print(f"MQTT client is fully ready and listening for messages")
            # Process any messages that arrived before we were ready
            await self.process_queued_messages()
        elif self.is_connected:
            print(f"Connected but subscription status unclear")
            print(f"   Connected: {self.is_connected}")
            print(f"   Subscribed: {self.is_subscribed}")
            print(f"   Continuing anyway - messages may still be received...")
            # Process any messages that arrived before we were ready
            await self.process_queued_messages()
        else:
            print(f"MQTT client failed to connect properly")
            print(f"   Connected: {self.is_connected}")
            print(f"   Subscribed: {self.is_subscribed}")
        
    async def stop(self):
        self.client.loop_stop()
        self.client.disconnect()

    async def publish_response(self, clientid: str, command: str, response: dict):
        """Publish response back to MQTT topic"""
        if self.is_connected:
            # Per broker ACL, responses must go under responses/, not commands/
            response_topic = f"devices/{clientid}/responses/electronic/{command}"
            response_payload = json.dumps(response, ensure_ascii=False)
            
            try:
                info = self.client.publish(response_topic, response_payload, qos=1, retain=False)
                if info.rc == 0:
                    # Wait for PUBACK when QoS=1 to confirm broker accepted it
                    info.wait_for_publish()
                    print(f"Response published to: {response_topic}")
                    print(f"   Payload: {response_payload}")
                    print(f"   mid={getattr(info, 'mid', '?')} (acknowledged)")
                else:
                    print(f"Failed to publish response (client-side rc): {info.rc}")
            except Exception as e:
                print(f"Error publishing response: {e}")
        else:
            print(f"Cannot publish response - not connected")

mqtt_client: Optional[AsyncMQTTClient] = None

async def mqtt_listener():
    global mqtt_client
    
    # Get the current event loop
    main_loop = asyncio.get_running_loop()
    
    # Validate required environment variables
    host = os.getenv("host")
    username = os.getenv("username")
    password = os.getenv("password")
    client_id = os.getenv("client_id")
    
    if not host:
        raise ValueError("Environment variable 'host' is required in .env file")
    if not username:
        raise ValueError("Environment variable 'username' is required in .env file")
    if not password:
        raise ValueError("Environment variable 'password' is required in .env file")
    if not client_id:
        raise ValueError("Environment variable 'client_id' is required in .env file")
    
    mqtt_client = AsyncMQTTClient(
        broker_host=host,
        broker_port=int(os.getenv("port")) if os.getenv("port") else None,
        username=username,
        password=password,
        client_id=client_id
    )
    
    # Set the main event loop reference
    mqtt_client.set_main_loop(main_loop)
    
    await mqtt_client.start()
    
    # Keep the task running
    try:
        while True:
            await asyncio.sleep(1)
    except asyncio.CancelledError:
        if mqtt_client:
            await mqtt_client.stop()

@app.on_event("startup")
async def startup_event():
    asyncio.create_task(mqtt_listener())

@app.on_event("shutdown")
async def shutdown_event():
    global mqtt_client
    if mqtt_client:
        await mqtt_client.stop()
